<!-- Module Include File -->
<!-- IMPORTANT: Tool and View Registration Pattern (DO NOT BREAK!) ============================================================= 1. BWbNavNodeTool subclasses MUST be registered as agents on "workbench:Workbench" to appear in Tools menu 2. Views register as agents on the tool type (e.g., "datasync:DataSyncTool") 3. Use @AgentOn annotation in class AND module-include.xml entry 4. Reference: niagaraModulesExample/envCtrlDriver 5. If tool disappears, revert to commit a770078 and add agent registration incrementally -->
<!-- Types -->
<types>
  <!-- com.mea.datasync.ui -->
  <!--com.mea.datasync.ui-->
  <!-- CRITICAL: Tool MUST have workbench:Workbench agent to appear in Tools menu -->
  <type class="com.mea.datasync.ui.BDataSyncTool" name="DataSyncTool"/>
  <!-- DataSync Manager - Register as agent on both DataSyncTool and DataSourceConnections -->
  <type class="com.mea.datasync.ui.BDataSourceConnectionManager" name="DataSourceConnectionManager">
    <agent>
      <on type="datasync:DataSyncTool"/>
      <on type="datasync:DataSourceConnections"/>
    </agent>
  </type>
  <!-- DataSync Model Components -->
  <!--com.mea.datasync.model-->
  <type class="com.mea.datasync.model.BDataSource" name="DataSourceConnection"/>
  <type class="com.mea.datasync.model.BTargetNiagaraStation" name="TargetNiagaraStation"/>

  <!-- Data Source Connection Architecture -->
  <type class="com.mea.datasync.model.BAbstractDataSourceConnection" name="AbstractDataSourceConnection"/>
  <type class="com.mea.datasync.model.BConnectionDetails" name="ConnectionDetails"/>
  <type class="com.mea.datasync.model.BAutoCheckConfig" name="AutoCheckConfig"/>

  <!-- Excel Data Source Implementation -->
  <type class="com.mea.datasync.model.BExcelDataSourceConnection" name="ExcelDataSourceConnection"/>
  <type class="com.mea.datasync.model.BExcelConnectionDetails" name="ExcelConnectionDetails"/>

  <!-- Container Components -->
  <type class="com.mea.datasync.model.BDataSourceConnections" name="DataSourceConnections"/>
  <type class="com.mea.datasync.model.BDataSourceConnectionsFolder" name="DataSourceConnectionsFolder"/>
</types>